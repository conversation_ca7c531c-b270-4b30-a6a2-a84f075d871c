import { GoogleGenerativeAI } from '@google/generative-ai';
import { GlobalNewsArticle } from './GlobalNewsService';
import { CompanyProfile } from './CompanyProfileService';
import { logger } from '../utils/logger';

export interface MarketImpactAnalysis {
  impactScore: number; // 0-100
  impactDirection: 'positive' | 'negative' | 'neutral';
  timeframe: 'immediate' | 'short_term' | 'medium_term' | 'long_term';
  confidence: number; // 0-100
  affectedSectors: SectorImpact[];
  affectedCompanies: CompanyImpact[];
  reasoning: string;
  tradingOpportunities: TradingOpportunity[];
  riskFactors: string[];
  catalysts: string[];
}

export interface SectorImpact {
  sector: string;
  impactScore: number; // -100 to 100
  reasoning: string;
  confidence: number;
  timeframe: string;
}

export interface CompanyImpact {
  companyName: string;
  ticker?: string;
  impactScore: number; // -100 to 100
  reasoning: string;
  confidence: number;
  sector: string;
}

export interface TradingOpportunity {
  type: 'long' | 'short' | 'pairs_trade' | 'sector_rotation';
  tickers: string[];
  reasoning: string;
  expectedReturn: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  confidence: number;
}

export class NewsImpactAnalysisService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('Google AI API key is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemma-3-27b-it' });
  }

  /**
   * Analyze market impact of news article with enhanced validation
   */
  public async analyzeMarketImpact(
    article: GlobalNewsArticle,
    companyProfiles?: CompanyProfile[]
  ): Promise<MarketImpactAnalysis> {
    try {
      const prompt = this.buildAnalysisPrompt(article, companyProfiles);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Log the raw response from the AI for debugging
      console.log('=== RAW AI RESPONSE ===');
      console.log('Article:', article.title.substring(0, 100));
      console.log('Response:', text);
      console.log('=== END RAW RESPONSE ===');

      logger.info('Raw AI response received', {
        title: article.title.substring(0, 100),
        rawText: text.substring(0, 500) + (text.length > 500 ? '...' : '')
      });

      const analysis = this.parseAnalysisResponse(text);
      
      if (analysis.confidence > 0) {
        logger.info('Market impact analysis completed successfully', {
          title: article.title.substring(0, 100),
          impactScore: analysis.impactScore,
          affectedSectors: analysis.affectedSectors.length,
          opportunities: analysis.tradingOpportunities.length
        });
      } else {
        logger.warn('Market impact analysis returned fallback data', {
          title: article.title.substring(0, 100)
        });
      }

      return analysis;
    } catch (error) {
      logger.error('Error analyzing market impact:', error);
      return this.getFallbackAnalysis();
    }
  }

  /**
   * Build enhanced analysis prompt with business context validation
   */
  private buildAnalysisPrompt(article: GlobalNewsArticle, companyProfiles?: CompanyProfile[]): string {
    const companyContext = companyProfiles ? this.buildCompanyContext(companyProfiles) : '';

    return `
You are a professional financial analyst with expertise in causal analysis and market impact assessment. Your task is to analyze news for genuine market impacts while avoiding false positive correlations.

**CRITICAL VALIDATION REQUIREMENTS:**
1. Only include tickers where you can trace a clear causal chain: News Event → Business Impact → Financial Effect → Market Reaction
2. Each causal link must have >70% confidence with explicit justification
3. Avoid word-similarity matches (e.g., "navigation" ≠ NAVI stock, "zoom" ≠ ZOOM stock unless about video conferencing)
4. Consider business model relevance, not just keyword matches
5. Validate that the news would actually affect the company's revenue streams or operations

**NEWS ARTICLE:**
Title: ${article.title}
Content: ${article.content}
Source: ${article.source}
Published: ${article.publishedAt.toISOString()}
Category: ${article.category}

${companyContext}

**VALIDATION QUESTIONS (Answer for each ticker):**
Before including any ticker, you MUST answer YES with high confidence to ALL these questions:
1. Does this news directly affect this company's core business operations or revenue streams?
2. Is there a clear, logical mechanism for financial impact (not just word association)?
3. Would a professional analyst agree this connection is valid and material?
4. Can you trace a specific causal path from the news event to stock price impact?

**ANALYSIS FRAMEWORK:**
Evaluate through rigorous causal analysis, focusing on genuine business impacts.

1. **Causal Chain Validation**
   - News Event: What specifically happened?
   - Business Mechanism: HOW does this affect specific companies' operations?
   - Financial Impact: WHAT financial metrics will be affected and by how much?
   - Market Reaction: WHY would investors react and in what direction?
   - Confidence Level: Rate each link in the chain (0-100%)

2. **Business Model Analysis**
   - Does the news affect the company's primary revenue sources?
   - Are there supply chain, regulatory, or competitive implications?
   - Consider geographic exposure and operational dependencies
   - Validate sector relevance beyond superficial keywords

3. **False Positive Prevention**
   - Check for word-similarity false matches
   - Verify business model relevance
   - Ensure causal mechanisms are specific and logical
   - Avoid generic sector mentions without company-specific impact

4. **Enhanced Company Analysis**
   - For each potential ticker, provide detailed causal reasoning
   - Include confidence scores for each causal link
   - Specify the business impact mechanism
   - Validate against known false positive patterns

**RESPONSE FORMAT:**
Provide analysis in valid JSON with enhanced causal validation fields:

\`\`\`json
{
  "impactScore": 0-100,
  "impactDirection": "positive|negative|neutral",
  "timeframe": "immediate|short_term|medium_term|long_term",
  "confidence": 0-100,
  "affectedSectors": [
    {
      "sector": "sector_name",
      "impactScore": -100 to 100,
      "reasoning": "detailed_causal_reasoning",
      "confidence": 0-100,
      "timeframe": "timeframe",
      "causalMechanism": "specific_mechanism_description"
    }
  ],
  "affectedCompanies": [
    {
      "companyName": "company_name",
      "ticker": "TICKER",
      "impactScore": -100 to 100,
      "reasoning": "detailed_causal_chain_explanation",
      "confidence": 0-100,
      "sector": "sector_name",
      "causalChain": {
        "newsToBusinessConfidence": 0-100,
        "businessToFinancialConfidence": 0-100,
        "financialToMarketConfidence": 0-100,
        "overallCausalStrength": 0-100
      },
      "businessImpactMechanism": "how_news_affects_business_operations",
      "falsePositiveCheck": "validation_that_this_is_not_word_similarity_match"
    }
  ],
  "reasoning": "comprehensive_causal_analysis_with_validation_steps",
  "tradingOpportunities": [
    {
      "type": "long|short|pairs_trade|sector_rotation",
      "tickers": ["TICKER1", "TICKER2"],
      "reasoning": "opportunity_explanation_with_causal_validation",
      "expectedReturn": "percentage",
      "riskLevel": "low|medium|high",
      "timeframe": "timeframe",
      "confidence": 0-100,
      "causalStrength": 0-100
    }
  ],
  "riskFactors": ["risk1", "risk2", "risk3"],
  "catalysts": ["catalyst1", "catalyst2", "catalyst3"],
  "validationNotes": "explanation_of_validation_process_and_any_rejected_tickers"
}
\`\`\`

**CRITICAL VALIDATION EXAMPLES:**
❌ WRONG: Including NAVI for "navigation system" news (word similarity, no business relevance)
✅ CORRECT: Including NAVI for "student loan policy" news (direct business model impact)

❌ WRONG: Including ZOOM for "zoom lens" news (word similarity)
✅ CORRECT: Including ZOOM for "remote work policy" news (business model relevance)

**MANDATORY VALIDATION STEPS:**
1. For each ticker, explicitly state the causal mechanism
2. Verify business model relevance (not just word matching)
3. Provide confidence scores for each causal link
4. Include falsePositiveCheck field explaining why this is NOT a false positive
5. Only include tickers where overallCausalStrength > 70%

Do not output any text before or after the JSON block.
`;
  }

  /**
   * Build company context for enhanced validation
   */
  private buildCompanyContext(companyProfiles: CompanyProfile[]): string {
    if (companyProfiles.length === 0) return '';

    const contextLines = companyProfiles.map(profile => {
      const revenueStreams = profile.revenueStreams
        .map(stream => `${stream.source} (${stream.percentage}%)`)
        .join(', ');

      return `${profile.ticker} (${profile.companyName}):
- Sector: ${profile.sector} | Industry: ${profile.industry}
- Business Model: ${profile.businessModel}
- Primary Revenue: ${revenueStreams || profile.primaryRevenue.join(', ')}
- Geographic Exposure: ${profile.geographicExposure.domestic}% domestic, ${profile.geographicExposure.international}% international
- Key Dependencies: Regulatory: ${profile.keyDependencies.regulatoryBodies.join(', ')}`;
    });

    return `
**COMPANY CONTEXT (Only consider if genuinely relevant):**
${contextLines.join('\n\n')}

**VALIDATION REQUIREMENT:** For each ticker above, you must validate that the news has a genuine business impact on their specific operations, not just word similarity.`;
  }

  /**
   * Parse AI analysis response
   */
  private parseAnalysisResponse(text: string): MarketImpactAnalysis {
    try {
      const jsonMatch = text.match(/```json([\s\S]*?)```/);
      if (jsonMatch && jsonMatch[1]) {
        const jsonString = jsonMatch[1].trim();
        const data = JSON.parse(jsonString);
        
        return {
          impactScore: data.impactScore || 0,
          impactDirection: data.impactDirection || 'neutral',
          timeframe: data.timeframe || 'short_term',
          confidence: data.confidence || 50,
          affectedSectors: data.affectedSectors || [],
          affectedCompanies: data.affectedCompanies || [],
          reasoning: data.reasoning || 'No analysis available',
          tradingOpportunities: data.tradingOpportunities || [],
          riskFactors: data.riskFactors || [],
          catalysts: data.catalysts || []
        };
      }

      // Fallback for cases where the AI doesn't use the markdown block
      const fallbackJsonMatch = text.match(/\{[\s\S]*\}/);
      if (fallbackJsonMatch) {
        logger.warn('Parsing fallback used; AI did not provide markdown JSON block.');
        const data = JSON.parse(fallbackJsonMatch[0]);
        return {
          impactScore: data.impactScore || 0,
          impactDirection: data.impactDirection || 'neutral',
          timeframe: data.timeframe || 'short_term',
          confidence: data.confidence || 50,
          affectedSectors: data.affectedSectors || [],
          affectedCompanies: data.affectedCompanies || [],
          reasoning: data.reasoning || 'No analysis available',
          tradingOpportunities: data.tradingOpportunities || [],
          riskFactors: data.riskFactors || [],
          catalysts: data.catalysts || []
        };
      }

      logger.error('Could not find or parse JSON in AI response', { responseText: text });
      return this.getFallbackAnalysis();
    } catch (error) {
      const e = error as Error;
      logger.error('Error parsing analysis response: ' + e.message, { 
        stack: e.stack,
        responseText: text 
      });
      return this.getFallbackAnalysis();
    }
  }

  /**
   * Fallback analysis for errors
   */
  private getFallbackAnalysis(): MarketImpactAnalysis {
    return {
      impactScore: 0,
      impactDirection: 'neutral',
      timeframe: 'short_term',
      confidence: 0,
      affectedSectors: [],
      affectedCompanies: [],
      reasoning: 'Analysis failed, unable to determine market impact',
      tradingOpportunities: [],
      riskFactors: ['Analysis uncertainty'],
      catalysts: []
    };
  }

  /**
   * Batch analyze multiple articles
   */
  public async batchAnalyzeArticles(
    articles: GlobalNewsArticle[],
    maxConcurrent: number = 5
  ): Promise<(GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]> {
    const results: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[] = [];
    
    // Process articles in batches to avoid rate limits
    for (let i = 0; i < articles.length; i += maxConcurrent) {
      const batch = articles.slice(i, i + maxConcurrent);
      const batchPromises = batch.map(async (article) => {
        const analysis = await this.analyzeMarketImpact(article);
        return { ...article, analysis };
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to respect rate limits
      if (i + maxConcurrent < articles.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Filter high-impact opportunities
   */
  public filterHighImpactOpportunities(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    minImpactScore: number = 60,
    minConfidence: number = 70
  ): TradingOpportunity[] {
    const opportunities: TradingOpportunity[] = [];

    analyzedArticles.forEach(article => {
      if (article.analysis.impactScore >= minImpactScore && 
          article.analysis.confidence >= minConfidence) {
        opportunities.push(...article.analysis.tradingOpportunities.filter(
          opp => opp.confidence >= minConfidence
        ));
      }
    });

    // Remove duplicates and sort by expected return
    const uniqueOpportunities = opportunities.filter((opp, index, self) => 
      index === self.findIndex(o => 
        o.type === opp.type && 
        JSON.stringify(o.tickers.sort()) === JSON.stringify(opp.tickers.sort())
      )
    );

    return uniqueOpportunities.sort((a, b) => b.expectedReturn - a.expectedReturn);
  }

  /**
   * Get sector impact summary
   */
  public getSectorImpactSummary(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]
  ): { [sector: string]: { totalImpact: number; articleCount: number; avgConfidence: number } } {
    const sectorSummary: { [sector: string]: { totalImpact: number; articleCount: number; avgConfidence: number } } = {};

    analyzedArticles.forEach(article => {
      article.analysis.affectedSectors.forEach(sectorImpact => {
        if (!sectorSummary[sectorImpact.sector]) {
          sectorSummary[sectorImpact.sector] = {
            totalImpact: 0,
            articleCount: 0,
            avgConfidence: 0
          };
        }

        sectorSummary[sectorImpact.sector].totalImpact += sectorImpact.impactScore;
        sectorSummary[sectorImpact.sector].articleCount += 1;
        sectorSummary[sectorImpact.sector].avgConfidence += sectorImpact.confidence;
      });
    });

    // Calculate averages
    Object.keys(sectorSummary).forEach(sector => {
      const summary = sectorSummary[sector];
      summary.avgConfidence = summary.avgConfidence / summary.articleCount;
    });

    return sectorSummary;
  }

  /**
   * Extract unique tickers from analysis
   */
  public extractTickers(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]
  ): { ticker: string; impactScore: number; confidence: number; opportunities: number }[] {
    const tickerMap = new Map<string, { impactScore: number; confidence: number; opportunities: number; count: number }>();

    analyzedArticles.forEach(article => {
      // From affected companies
      article.analysis.affectedCompanies.forEach(company => {
        if (company.ticker) {
          const existing = tickerMap.get(company.ticker) || { impactScore: 0, confidence: 0, opportunities: 0, count: 0 };
          existing.impactScore += company.impactScore;
          existing.confidence += company.confidence;
          existing.count += 1;
          tickerMap.set(company.ticker, existing);
        }
      });

      // From trading opportunities
      article.analysis.tradingOpportunities.forEach(opportunity => {
        opportunity.tickers.forEach(ticker => {
          const existing = tickerMap.get(ticker) || { impactScore: 0, confidence: 0, opportunities: 0, count: 0 };
          existing.opportunities += 1;
          tickerMap.set(ticker, existing);
        });
      });
    });

    // Convert to array and calculate averages
    return Array.from(tickerMap.entries()).map(([ticker, data]) => ({
      ticker,
      impactScore: data.count > 0 ? data.impactScore / data.count : 0,
      confidence: data.count > 0 ? data.confidence / data.count : 0,
      opportunities: data.opportunities
    })).sort((a, b) => Math.abs(b.impactScore) - Math.abs(a.impactScore));
  }
}
