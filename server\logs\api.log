[2025-06-23 09:56:37.110] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:56:45.210] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:56:45.222] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (12ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:56:45.224] [INFO] [API]: GET /api/tickers - Status 200 (12ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:56:52.935] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:57:06.955] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:57:25.257] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 09:57:40.175] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:57:40.176] [INFO] [General]: Generated recommendation for LMT | {"service":"trading-bot","recommendation":{"ticker":"LMT","action":"Hold","confidence":"Medium","technicalSignals":{"rsi":"neutral","macd":"bearish","bollingerBands":"neutral","sma":"bullish"},"sentiment":"Neutral","reasoning":"Neutral signals, holding position.","riskManagement":{"stopLoss":445.43160668540713,"takeProfit":520.8167866291857,"positionSize":1000,"maxRisk":0.02},"timestamp":"2025-06-23T06:57:40.176Z"}}
[2025-06-23 09:57:40.227] [INFO] [DataService] - fetchHistoricalData: Starting historical data fetch | {"service":"trading-bot","ticker":"RTX","startDate":"2023-01-01"}
[2025-06-23 09:57:40.765] [INFO] [DataService] - fetchHistoricalData: Successfully fetched historical data (537ms) | {"service":"trading-bot","ticker":"RTX","recordCount":618,"dateRange":{"from":"2023-01-03T14:30:00.000Z","to":"2025-06-20T13:30:00.000Z"}}
[2025-06-23 09:57:40.766] [INFO] [DataService] - fetchCurrentPrice: Starting current price fetch | {"service":"trading-bot","ticker":"RTX"}
[2025-06-23 09:57:40.925] [INFO] [DataService] - fetchCurrentPrice: Successfully fetched current price (159ms) | {"service":"trading-bot","ticker":"RTX","currentPrice":146.64,"marketState":"PREPRE","lastUpdated":"2025-06-20T20:00:02.000Z"}
[2025-06-23 09:57:40.943] [INFO] [General]: Technical indicators calculated successfully | {"service":"trading-bot","indicators":{"rsi":67.55,"macd":{"macd":3.9098971743455877,"signal":3.3212177561387213,"histogram":0.5886794182068664},"bollingerBands":{"upper":149.21649038181047,"middle":139.59250183105468,"lower":129.96851328029888},"sma":{"short":139.59250183105468,"long":132.94280029296874,"crossover":"bullish"},"atr":3.1647760785687544,"adx":3.23}}
[2025-06-23 09:57:41.901] [INFO] [General]: Fetched 20 recent news articles for RTX | {"service":"trading-bot"}
[2025-06-23 09:57:45.218] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:57:45.231] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (13ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:57:45.233] [INFO] [API]: GET /api/tickers - Status 200 (13ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:57:53.343] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:02.187] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:10.856] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:18.748] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:28.882] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:36.000] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:45.214] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:58:45.225] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (11ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:58:45.226] [INFO] [API]: GET /api/tickers - Status 200 (11ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:58:46.816] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:59.388] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 09:59:09.355] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:59:20.957] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:59:26.410] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:59:34.781] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:59:39.730] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:59:39.743] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (13ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:59:39.744] [INFO] [API]: GET /api/tickers - Status 200 (13ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:59:45.215] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:59:45.223] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (8ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:59:45.224] [INFO] [API]: GET /api/tickers - Status 200 (8ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:59:49.546] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"High"}
[2025-06-23 10:00:00.712] [INFO] [DataService] - fetchHistoricalData: Starting historical data fetch | {"service":"trading-bot","ticker":"LMT","startDate":"2023-01-01"}
[2025-06-23 10:00:01.140] [INFO] [DataService] - fetchHistoricalData: Successfully fetched historical data (427ms) | {"service":"trading-bot","ticker":"LMT","recordCount":618,"dateRange":{"from":"2023-01-03T14:30:00.000Z","to":"2025-06-20T13:30:00.000Z"}}
[2025-06-23 10:00:01.141] [INFO] [DataService] - fetchCurrentPrice: Starting current price fetch | {"service":"trading-bot","ticker":"LMT"}
[2025-06-23 10:00:01.237] [INFO] [DataService] - fetchCurrentPrice: Successfully fetched current price (96ms) | {"service":"trading-bot","ticker":"LMT","currentPrice":470.56,"marketState":"PREPRE","lastUpdated":"2025-06-20T20:00:02.000Z"}
[2025-06-23 10:00:01.246] [INFO] [General]: Technical indicators calculated successfully | {"service":"trading-bot","indicators":{"rsi":48.85,"macd":{"macd":0.5045616007407716,"signal":1.5152854012870949,"histogram":-1.0107238005463233},"bollingerBands":{"upper":488.88628348940074,"middle":474.93800201416013,"lower":460.9897205389195},"sma":{"short":474.93800201416013,"long":471.76919921875,"crossover":"bullish"},"atr":12.56419665729643,"adx":39.57}}
[2025-06-23 10:00:02.055] [INFO] [General]: Fetched 5 recent news articles for LMT | {"service":"trading-bot"}
[2025-06-23 10:00:04.402] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:00:17.199] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"Low"}
[2025-06-23 10:00:19.154] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:00:30.709] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:00:34.915] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"Medium"}
[2025-06-23 10:00:41.970] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:00:45.222] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:00:45.243] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (21ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 10:00:45.244] [INFO] [API]: GET /api/tickers - Status 200 (21ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 10:00:47.681] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:01:31.483] [INFO] [General]: Initialized default tickers | {"service":"trading-bot","count":6}
[2025-06-23 10:01:31.496] [WARN] [General]: No OpenAI API key provided for embeddings, using fallback similarity methods | {"service":"trading-bot"}
[2025-06-23 10:01:31.500] [INFO] [General]: Multi-model ensemble initialized with 3 models | {"service":"trading-bot"}
[2025-06-23 10:01:31.502] [INFO] [General]: Initialized 5 test cases for discovery validation | {"service":"trading-bot"}
[2025-06-23 10:01:31.654] [INFO] [General]: Connected to MongoDB successfully | {"service":"trading-bot"}
[2025-06-23 10:01:31.660] [INFO] [General]: Server running on port 3002 | {"service":"trading-bot"}
[2025-06-23 10:01:31.662] [INFO] [General]: Environment: development | {"service":"trading-bot"}
[2025-06-23 10:01:31.713] [INFO] [General]: Data update job started | {"service":"trading-bot"}
[2025-06-23 10:01:45.214] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:01:45.283] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (69ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 10:01:45.284] [INFO] [API]: GET /api/tickers - Status 200 (69ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 10:03:58.169] [INFO] [General]: Initialized default tickers | {"service":"trading-bot","count":6}
[2025-06-23 10:03:58.176] [WARN] [General]: No OpenAI API key provided for embeddings, using fallback similarity methods | {"service":"trading-bot"}
[2025-06-23 10:03:58.177] [INFO] [General]: Multi-model ensemble initialized with 3 models | {"service":"trading-bot"}
[2025-06-23 10:03:58.178] [INFO] [General]: Initialized 5 test cases for discovery validation | {"service":"trading-bot"}
[2025-06-23 10:03:58.291] [INFO] [General]: Connected to MongoDB successfully | {"service":"trading-bot"}
[2025-06-23 10:03:58.302] [INFO] [General]: Server running on port 3002 | {"service":"trading-bot"}
[2025-06-23 10:03:58.303] [INFO] [General]: Environment: development | {"service":"trading-bot"}
[2025-06-23 10:03:58.364] [INFO] [General]: Data update job started | {"service":"trading-bot"}
[2025-06-23 10:04:45.241] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:04:45.291] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (50ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:04:45.293] [INFO] [API]: GET /api/tickers - Status 200 (50ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:04:58.506] [INFO] [API] - GET /api/tickers/test: Fetching specific ticker data | {"service":"trading-bot","ticker":"TEST","query":{}}
[2025-06-23 10:04:58.515] [WARN] [API] - GET /api/tickers/test: Ticker not found (9ms) | {"service":"trading-bot","ticker":"TEST"}
[2025-06-23 10:04:58.516] [INFO] [API]: GET /api/tickers/test - Status 404 (9ms) | {"service":"trading-bot","ticker":"TEST"}
[2025-06-23 10:06:19.586] [INFO] [General]: Initialized default tickers | {"service":"trading-bot","count":6}
[2025-06-23 10:06:19.595] [WARN] [General]: No OpenAI API key provided for embeddings, using fallback similarity methods | {"service":"trading-bot"}
[2025-06-23 10:06:19.596] [INFO] [General]: Multi-model ensemble initialized with 3 models | {"service":"trading-bot"}
[2025-06-23 10:06:19.600] [INFO] [General]: Initialized 5 test cases for discovery validation | {"service":"trading-bot"}
[2025-06-23 10:06:19.792] [INFO] [General]: Connected to MongoDB successfully | {"service":"trading-bot"}
[2025-06-23 10:06:19.802] [INFO] [General]: Server running on port 3002 | {"service":"trading-bot"}
[2025-06-23 10:06:19.803] [INFO] [General]: Environment: development | {"service":"trading-bot"}
[2025-06-23 10:06:19.871] [INFO] [General]: Data update job started | {"service":"trading-bot"}
[2025-06-23 10:06:45.239] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:06:45.299] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (60ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:06:45.301] [INFO] [API]: GET /api/tickers - Status 200 (60ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:06:53.921] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:06:53.934] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (13ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:06:53.935] [INFO] [API]: GET /api/tickers - Status 200 (13ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:37.115] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:07:37.142] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (27ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:37.144] [INFO] [API]: GET /api/tickers - Status 200 (27ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:37.266] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:07:37.292] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (26ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:37.295] [INFO] [API]: GET /api/tickers - Status 200 (26ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:45.266] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:07:45.295] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (29ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:45.297] [INFO] [API]: GET /api/tickers - Status 200 (29ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:21.329] [INFO] [API] - GET /api/tickers/XOM: Fetching specific ticker data | {"service":"trading-bot","ticker":"XOM","query":{}}
[2025-06-23 10:08:21.407] [INFO] [API] - GET /api/tickers/XOM: Successfully fetched ticker data (78ms) | {"service":"trading-bot","ticker":"XOM","lastUpdated":"2025-06-23T07:02:30.558Z","currentPrice":114.7,"newsCount":1}
[2025-06-23 10:08:21.408] [INFO] [API]: GET /api/tickers/XOM - Status 200 (78ms) | {"service":"trading-bot","ticker":"XOM","newsCount":1}
[2025-06-23 10:08:29.691] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:08:29.697] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:29.700] [INFO] [API]: GET /api/tickers - Status 200 (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:29.706] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:08:29.712] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (7ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:29.713] [INFO] [API]: GET /api/tickers - Status 200 (7ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:45.223] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:08:45.229] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:45.230] [INFO] [API]: GET /api/tickers - Status 200 (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:09:45.244] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:09:45.252] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (9ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:09:45.252] [INFO] [API]: GET /api/tickers - Status 200 (9ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:10:45.294] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:10:45.304] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (10ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:10:45.306] [INFO] [API]: GET /api/tickers - Status 200 (10ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:11:45.217] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:11:45.223] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:11:45.223] [INFO] [API]: GET /api/tickers - Status 200 (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:12:47.023] [INFO] [General]: Initialized default tickers | {"service":"trading-bot","count":6}
[2025-06-23 10:12:47.030] [INFO] [General]: Google AI embeddings initialized | {"service":"trading-bot"}
[2025-06-23 10:12:47.032] [INFO] [General]: Multi-model ensemble initialized with 3 models | {"service":"trading-bot"}
[2025-06-23 10:12:47.033] [INFO] [General]: Initialized 5 test cases for discovery validation | {"service":"trading-bot"}
[2025-06-23 10:12:47.169] [INFO] [General]: Connected to MongoDB successfully | {"service":"trading-bot"}
[2025-06-23 10:12:47.178] [INFO] [General]: Server running on port 3002 | {"service":"trading-bot"}
[2025-06-23 10:12:47.179] [INFO] [General]: Environment: development | {"service":"trading-bot"}
[2025-06-23 10:12:47.230] [INFO] [General]: Data update job started | {"service":"trading-bot"}
[2025-06-23 10:13:06.405] [INFO] [General]: Running full discovery test suite | {"service":"trading-bot"}
[2025-06-23 10:13:06.406] [INFO] [General]: Running test suite with 5 test cases | {"service":"trading-bot"}
[2025-06-23 10:13:06.407] [INFO] [Discovery] - Start: Starting ticker discovery process | {"service":"trading-bot","lookbackHours":24,"categories":["general","world","nation","technology","science","health"]}
[2025-06-23 10:13:08.442] [INFO] [General]: Global news fetched successfully | {"service":"trading-bot","totalArticles":66,"sources":["Politico","BBC News","The Washington Post","Understandingwar.org","Vox","Council on Foreign Relations","Phys.Org","Associated Press","SciTechDaily","Business Insider","NPR","TechCrunch","Eurogamer.net","Thinkstewartville.com","NDTV News","TooFab","The Daily Memphian","Entertainment Weekly","9to5google.com","RPG Site","MacRumors","The Daily Galaxy --Great Discoveries Channel","Space.com","Earth.com","Good News Network","Delish.com","Dailykos.com","PsyPost","Yahoo Entertainment","Heat.com","Albuquerque Journal","PBS","Ars Technica","Newsnationnow.com","Nintendo Life","Slate Magazine","Nintendoeverything.com","The Verge","Notebookcheck.net","Farmingdale-observer.com","ScienceAlert","IFLScience","Indiandefencereview.com","404media.co","St George News","Eatingwell.com","Vnexpress.net","Deccan Herald","Glassalmanac.com","Fox News"],"categories":["general","health","nation","science","technology"]}
[2025-06-23 10:13:08.444] [INFO] [Discovery] - SemanticFilter: Filtered 66 → 11 → 0 articles | {"service":"trading-bot","originalCount":66,"traditionalFiltered":11,"semanticallyFiltered":0}
[2025-06-23 10:13:08.445] [INFO] [Discovery] - Complete: Ticker discovery completed. Found 0 new tickers. | {"service":"trading-bot","totalNews":66,"relevantNews":0,"analyzedArticles":0,"discoveredTickers":0,"finalTickers":0}
[2025-06-23 10:13:08.446] [INFO] [General]: Test navi_navigation_fp: PASSED | {"service":"trading-bot","accuracy":1,"precision":0,"recall":0,"processingTime":2039}
[2025-06-23 10:13:08.448] [INFO] [Discovery] - Start: Starting ticker discovery process | {"service":"trading-bot","lookbackHours":24,"categories":["general","world","nation","technology","science","health"]}
[2025-06-23 10:13:09.694] [INFO] [General]: Global news fetched successfully | {"service":"trading-bot","totalArticles":66,"sources":["Politico","BBC News","The Washington Post","Understandingwar.org","Vox","Council on Foreign Relations","Phys.Org","Associated Press","SciTechDaily","Business Insider","NPR","TechCrunch","Eurogamer.net","Thinkstewartville.com","NDTV News","TooFab","The Daily Memphian","Entertainment Weekly","9to5google.com","RPG Site","MacRumors","The Daily Galaxy --Great Discoveries Channel","Space.com","Earth.com","Good News Network","Delish.com","Dailykos.com","PsyPost","Yahoo Entertainment","Heat.com","Albuquerque Journal","PBS","Ars Technica","Newsnationnow.com","Nintendo Life","Slate Magazine","Nintendoeverything.com","The Verge","Notebookcheck.net","Farmingdale-observer.com","ScienceAlert","IFLScience","Indiandefencereview.com","404media.co","St George News","Eatingwell.com","Vnexpress.net","Deccan Herald","Glassalmanac.com","Fox News"],"categories":["general","health","nation","science","technology"]}
[2025-06-23 10:13:09.696] [INFO] [Discovery] - SemanticFilter: Filtered 66 → 11 → 0 articles | {"service":"trading-bot","originalCount":66,"traditionalFiltered":11,"semanticallyFiltered":0}
[2025-06-23 10:13:09.697] [INFO] [Discovery] - Complete: Ticker discovery completed. Found 0 new tickers. | {"service":"trading-bot","totalNews":66,"relevantNews":0,"analyzedArticles":0,"discoveredTickers":0,"finalTickers":0}
[2025-06-23 10:13:09.698] [INFO] [General]: Test zoom_camera_fp: PASSED | {"service":"trading-bot","accuracy":1,"precision":0,"recall":0,"processingTime":1250}
[2025-06-23 10:13:09.699] [INFO] [Discovery] - Start: Starting ticker discovery process | {"service":"trading-bot","lookbackHours":24,"categories":["general","world","nation","technology","science","health"]}
[2025-06-23 10:13:10.934] [INFO] [General]: Global news fetched successfully | {"service":"trading-bot","totalArticles":66,"sources":["Politico","BBC News","The Washington Post","Understandingwar.org","Vox","Council on Foreign Relations","Phys.Org","Associated Press","SciTechDaily","Business Insider","NPR","TechCrunch","Eurogamer.net","Thinkstewartville.com","NDTV News","TooFab","The Daily Memphian","Entertainment Weekly","9to5google.com","RPG Site","MacRumors","The Daily Galaxy --Great Discoveries Channel","Space.com","Earth.com","Good News Network","Delish.com","Dailykos.com","PsyPost","Yahoo Entertainment","Heat.com","Albuquerque Journal","PBS","Ars Technica","Newsnationnow.com","Nintendo Life","Slate Magazine","Nintendoeverything.com","The Verge","Notebookcheck.net","Farmingdale-observer.com","ScienceAlert","IFLScience","Indiandefencereview.com","404media.co","St George News","Eatingwell.com","Vnexpress.net","Deccan Herald","Glassalmanac.com","Fox News"],"categories":["general","health","nation","science","technology"]}
[2025-06-23 10:13:10.936] [INFO] [Discovery] - SemanticFilter: Filtered 66 → 11 → 0 articles | {"service":"trading-bot","originalCount":66,"traditionalFiltered":11,"semanticallyFiltered":0}
[2025-06-23 10:13:10.937] [INFO] [Discovery] - Complete: Ticker discovery completed. Found 0 new tickers. | {"service":"trading-bot","totalNews":66,"relevantNews":0,"analyzedArticles":0,"discoveredTickers":0,"finalTickers":0}
[2025-06-23 10:13:10.938] [INFO] [General]: Test navi_student_loan_tp: PASSED | {"service":"trading-bot","accuracy":1,"precision":1,"recall":1,"processingTime":1239}
[2025-06-23 10:13:10.938] [INFO] [Discovery] - Start: Starting ticker discovery process | {"service":"trading-bot","lookbackHours":24,"categories":["general","world","nation","technology","science","health"]}
[2025-06-23 10:13:12.162] [INFO] [General]: Global news fetched successfully | {"service":"trading-bot","totalArticles":66,"sources":["Politico","BBC News","The Washington Post","Understandingwar.org","Vox","Council on Foreign Relations","Phys.Org","Associated Press","SciTechDaily","Business Insider","NPR","TechCrunch","Eurogamer.net","Thinkstewartville.com","NDTV News","TooFab","The Daily Memphian","Entertainment Weekly","9to5google.com","RPG Site","MacRumors","The Daily Galaxy --Great Discoveries Channel","Space.com","Earth.com","Good News Network","Delish.com","Dailykos.com","PsyPost","Yahoo Entertainment","Heat.com","Albuquerque Journal","PBS","Ars Technica","Newsnationnow.com","Nintendo Life","Slate Magazine","Nintendoeverything.com","The Verge","Notebookcheck.net","Farmingdale-observer.com","ScienceAlert","IFLScience","Indiandefencereview.com","404media.co","St George News","Eatingwell.com","Vnexpress.net","Deccan Herald","Glassalmanac.com","Fox News"],"categories":["general","health","nation","science","technology"]}
[2025-06-23 10:13:12.166] [INFO] [Discovery] - SemanticFilter: Filtered 66 → 11 → 0 articles | {"service":"trading-bot","originalCount":66,"traditionalFiltered":11,"semanticallyFiltered":0}
[2025-06-23 10:13:12.167] [INFO] [Discovery] - Complete: Ticker discovery completed. Found 0 new tickers. | {"service":"trading-bot","totalNews":66,"relevantNews":0,"analyzedArticles":0,"discoveredTickers":0,"finalTickers":0}
[2025-06-23 10:13:12.168] [INFO] [General]: Test zoom_remote_work_tp: PASSED | {"service":"trading-bot","accuracy":1,"precision":1,"recall":1,"processingTime":1230}
[2025-06-23 10:13:12.169] [INFO] [Discovery] - Start: Starting ticker discovery process | {"service":"trading-bot","lookbackHours":24,"categories":["general","world","nation","technology","science","health"]}
[2025-06-23 10:13:13.359] [INFO] [General]: Global news fetched successfully | {"service":"trading-bot","totalArticles":66,"sources":["Politico","BBC News","The Washington Post","Understandingwar.org","Vox","Council on Foreign Relations","Phys.Org","Associated Press","SciTechDaily","Business Insider","NPR","TechCrunch","Eurogamer.net","Thinkstewartville.com","NDTV News","TooFab","The Daily Memphian","Entertainment Weekly","9to5google.com","RPG Site","MacRumors","The Daily Galaxy --Great Discoveries Channel","Space.com","Earth.com","Good News Network","Delish.com","Dailykos.com","PsyPost","Yahoo Entertainment","Heat.com","Albuquerque Journal","PBS","Ars Technica","Newsnationnow.com","Nintendo Life","Slate Magazine","Nintendoeverything.com","The Verge","Notebookcheck.net","Farmingdale-observer.com","ScienceAlert","IFLScience","Indiandefencereview.com","404media.co","St George News","Eatingwell.com","Vnexpress.net","Deccan Herald","Glassalmanac.com","Fox News"],"categories":["general","health","nation","science","technology"]}
[2025-06-23 10:13:13.360] [INFO] [Discovery] - SemanticFilter: Filtered 66 → 11 → 0 articles | {"service":"trading-bot","originalCount":66,"traditionalFiltered":11,"semanticallyFiltered":0}
[2025-06-23 10:13:13.361] [INFO] [Discovery] - Complete: Ticker discovery completed. Found 0 new tickers. | {"service":"trading-bot","totalNews":66,"relevantNews":0,"analyzedArticles":0,"discoveredTickers":0,"finalTickers":0}
[2025-06-23 10:13:13.362] [INFO] [General]: Test apple_fruit_edge: PASSED | {"service":"trading-bot","accuracy":1,"precision":0,"recall":0,"processingTime":1193}
[2025-06-23 10:13:13.363] [INFO] [General]: API | {"service":"trading-bot"}
[2025-06-23 10:13:45.218] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:13:45.222] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (4ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:13:45.223] [INFO] [API]: GET /api/tickers - Status 200 (4ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:14:45.222] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:14:45.228] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:14:45.229] [INFO] [API]: GET /api/tickers - Status 200 (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:15:00.424] [INFO] [DataService] - fetchHistoricalData: Starting historical data fetch | {"service":"trading-bot","ticker":"LMT","startDate":"2023-01-01"}
[2025-06-23 10:15:00.863] [INFO] [DataService] - fetchHistoricalData: Successfully fetched historical data (439ms) | {"service":"trading-bot","ticker":"LMT","recordCount":618,"dateRange":{"from":"2023-01-03T14:30:00.000Z","to":"2025-06-20T13:30:00.000Z"}}
[2025-06-23 10:15:00.864] [INFO] [DataService] - fetchCurrentPrice: Starting current price fetch | {"service":"trading-bot","ticker":"LMT"}
[2025-06-23 10:15:02.611] [INFO] [DataService] - fetchCurrentPrice: Successfully fetched current price (1747ms) | {"service":"trading-bot","ticker":"LMT","currentPrice":470.56,"marketState":"PREPRE","lastUpdated":"2025-06-20T20:00:02.000Z"}
[2025-06-23 10:15:02.625] [INFO] [General]: Technical indicators calculated successfully | {"service":"trading-bot","indicators":{"rsi":48.85,"macd":{"macd":0.5045616007407716,"signal":1.5152854012870949,"histogram":-1.0107238005463233},"bollingerBands":{"upper":488.88628348940074,"middle":474.93800201416013,"lower":460.9897205389195},"sma":{"short":474.93800201416013,"long":471.76919921875,"crossover":"bullish"},"atr":12.56419665729643,"adx":39.57}}
[2025-06-23 10:15:03.236] [INFO] [General]: Fetched 5 recent news articles for LMT | {"service":"trading-bot"}
[2025-06-23 10:15:18.590] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:15:31.604] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:15:41.526] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"Medium"}
[2025-06-23 10:15:45.218] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:15:45.223] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (5ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:15:45.224] [INFO] [API]: GET /api/tickers - Status 200 (5ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:15:56.567] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:16:10.617] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:16:10.618] [INFO] [General]: Generated recommendation for LMT | {"service":"trading-bot","recommendation":{"ticker":"LMT","action":"Hold","confidence":"Medium","technicalSignals":{"rsi":"neutral","macd":"bearish","bollingerBands":"neutral","sma":"bullish"},"sentiment":"Neutral","reasoning":"Neutral signals, holding position.","riskManagement":{"stopLoss":445.43160668540713,"takeProfit":520.8167866291857,"positionSize":1000,"maxRisk":0.02},"timestamp":"2025-06-23T07:16:10.618Z"}}
[2025-06-23 10:16:10.650] [INFO] [DataService] - fetchHistoricalData: Starting historical data fetch | {"service":"trading-bot","ticker":"RTX","startDate":"2023-01-01"}
[2025-06-23 10:16:11.103] [INFO] [DataService] - fetchHistoricalData: Successfully fetched historical data (453ms) | {"service":"trading-bot","ticker":"RTX","recordCount":618,"dateRange":{"from":"2023-01-03T14:30:00.000Z","to":"2025-06-20T13:30:00.000Z"}}
[2025-06-23 10:16:11.104] [INFO] [DataService] - fetchCurrentPrice: Starting current price fetch | {"service":"trading-bot","ticker":"RTX"}
[2025-06-23 10:16:11.210] [INFO] [DataService] - fetchCurrentPrice: Successfully fetched current price (106ms) | {"service":"trading-bot","ticker":"RTX","currentPrice":146.64,"marketState":"PREPRE","lastUpdated":"2025-06-20T20:00:02.000Z"}
[2025-06-23 10:16:11.220] [INFO] [General]: Technical indicators calculated successfully | {"service":"trading-bot","indicators":{"rsi":67.55,"macd":{"macd":3.9098971743455877,"signal":3.3212177561387213,"histogram":0.5886794182068664},"bollingerBands":{"upper":149.21649038181047,"middle":139.59250183105468,"lower":129.96851328029888},"sma":{"short":139.59250183105468,"long":132.94280029296874,"crossover":"bullish"},"atr":3.1647760785687544,"adx":3.23}}
[2025-06-23 10:16:12.028] [INFO] [General]: Fetched 21 recent news articles for RTX | {"service":"trading-bot"}
[2025-06-23 10:16:21.110] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:16:27.901] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:16:39.380] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:16:45.223] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:16:45.228] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (5ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:16:45.229] [INFO] [API]: GET /api/tickers - Status 200 (5ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:16:50.961] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:17:01.784] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:17:12.564] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:17:28.780] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:17:36.782] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:17:44.803] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:17:45.221] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:17:45.225] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (4ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:17:45.226] [INFO] [API]: GET /api/tickers - Status 200 (4ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:17:53.540] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:18:04.312] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:18:18.246] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"High"}
[2025-06-23 10:18:32.673] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:18:41.082] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:18:45.222] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:18:45.228] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:18:45.229] [INFO] [API]: GET /api/tickers - Status 200 (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:18:58.469] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:19:13.133] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:19:25.438] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:19:41.929] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"Low"}
[2025-06-23 10:19:45.219] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:19:45.223] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (4ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:19:45.224] [INFO] [API]: GET /api/tickers - Status 200 (4ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:19:55.417] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:20:12.201] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:20:23.824] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:20:23.825] [INFO] [General]: Generated recommendation for RTX | {"service":"trading-bot","recommendation":{"ticker":"RTX","action":"Hold","confidence":"Medium","technicalSignals":{"rsi":"neutral","macd":"bullish","bollingerBands":"neutral","sma":"bullish"},"sentiment":"Neutral","reasoning":"Neutral signals, holding position.","riskManagement":{"stopLoss":140.3104478428625,"takeProfit":159.299104314275,"positionSize":1000,"maxRisk":0.02},"timestamp":"2025-06-23T07:20:23.825Z"}}
[2025-06-23 10:20:23.884] [INFO] [DataService] - fetchHistoricalData: Starting historical data fetch | {"service":"trading-bot","ticker":"NOC","startDate":"2023-01-01"}
[2025-06-23 10:20:24.301] [INFO] [DataService] - fetchHistoricalData: Successfully fetched historical data (417ms) | {"service":"trading-bot","ticker":"NOC","recordCount":618,"dateRange":{"from":"2023-01-03T14:30:00.000Z","to":"2025-06-20T13:30:00.000Z"}}
[2025-06-23 10:20:24.302] [INFO] [DataService] - fetchCurrentPrice: Starting current price fetch | {"service":"trading-bot","ticker":"NOC"}
[2025-06-23 10:20:24.406] [INFO] [DataService] - fetchCurrentPrice: Successfully fetched current price (104ms) | {"service":"trading-bot","ticker":"NOC","currentPrice":497.7,"marketState":"PREPRE","lastUpdated":"2025-06-20T20:00:02.000Z"}
[2025-06-23 10:20:24.412] [INFO] [General]: Technical indicators calculated successfully | {"service":"trading-bot","indicators":{"rsi":54.48,"macd":{"macd":4.582448593491165,"signal":3.157600199566651,"histogram":1.4248483939245138},"bollingerBands":{"upper":510.38914314694614,"middle":488.8009979248047,"lower":467.2128527026632},"sma":{"short":488.8009979248047,"long":490.3924005126953,"crossover":"bearish"},"atr":11.206804018594566,"adx":80.89}}
[2025-06-23 10:20:25.370] [INFO] [General]: Fetched 3 recent news articles for NOC | {"service":"trading-bot"}
[2025-06-23 10:20:43.735] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"NOC","sentiment":"Neutral","confidence":"Medium"}
[2025-06-23 10:20:45.219] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:20:45.233] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (13ms) | {"service":"trading-bot","tickerCount":4}
[2025-06-23 10:20:45.234] [INFO] [API]: GET /api/tickers - Status 200 (13ms) | {"service":"trading-bot","tickerCount":4}
[2025-06-23 10:20:55.528] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"NOC","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:21:02.627] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"NOC","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:21:02.628] [INFO] [General]: Generated recommendation for NOC | {"service":"trading-bot","recommendation":{"ticker":"NOC","action":"Hold","confidence":"Medium","technicalSignals":{"rsi":"neutral","macd":"bullish","bollingerBands":"neutral","sma":"bearish"},"sentiment":"Neutral","reasoning":"Neutral signals, holding position.","riskManagement":{"stopLoss":475.28639196281085,"takeProfit":542.5272160743782,"positionSize":1000,"maxRisk":0.02},"timestamp":"2025-06-23T07:21:02.628Z"}}
[2025-06-23 10:21:02.641] [INFO] [DataService] - fetchHistoricalData: Starting historical data fetch | {"service":"trading-bot","ticker":"XOM","startDate":"2023-01-01"}
[2025-06-23 10:21:03.086] [INFO] [DataService] - fetchHistoricalData: Successfully fetched historical data (445ms) | {"service":"trading-bot","ticker":"XOM","recordCount":618,"dateRange":{"from":"2023-01-03T14:30:00.000Z","to":"2025-06-20T13:30:00.000Z"}}
[2025-06-23 10:21:03.087] [INFO] [DataService] - fetchCurrentPrice: Starting current price fetch | {"service":"trading-bot","ticker":"XOM"}
[2025-06-23 10:21:03.220] [INFO] [DataService] - fetchCurrentPrice: Successfully fetched current price (133ms) | {"service":"trading-bot","ticker":"XOM","currentPrice":114.7,"marketState":"PREPRE","lastUpdated":"2025-06-20T20:00:33.000Z"}
[2025-06-23 10:21:03.229] [INFO] [General]: Technical indicators calculated successfully | {"service":"trading-bot","indicators":{"rsi":71.41,"macd":{"macd":2.0893155162860495,"signal":0.8660605933434677,"histogram":1.2232549229425818},"bollingerBands":{"upper":115.48167248128465,"middle":106.47950096130371,"lower":97.47732944132278},"sma":{"short":106.47950096130371,"long":106.27620040893555,"crossover":"bullish"},"atr":2.234209447131362,"adx":8.73}}
[2025-06-23 10:21:04.217] [INFO] [General]: Fetched 20 recent news articles for XOM | {"service":"trading-bot"}
[2025-06-23 10:21:16.341] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"XOM","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:21:31.407] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"XOM","sentiment":"Neutral","confidence":"Medium"}
[2025-06-23 10:21:45.221] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:21:45.230] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (8ms) | {"service":"trading-bot","tickerCount":5}
[2025-06-23 10:21:45.230] [INFO] [API]: GET /api/tickers - Status 200 (8ms) | {"service":"trading-bot","tickerCount":5}
[2025-06-23 10:21:47.008] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"XOM","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:22:00.175] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"XOM","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:22:02.720] [INFO] [General]: [SYSTEM] SIGINT received. Shutting down... | {"service":"trading-bot"}
[2025-06-23 10:22:30.012] [INFO] [General]: Initialized default tickers | {"service":"trading-bot","count":6}
[2025-06-23 10:22:30.017] [INFO] [General]: Google AI embeddings initialized | {"service":"trading-bot"}
[2025-06-23 10:22:30.018] [INFO] [General]: Multi-model ensemble initialized with 3 models | {"service":"trading-bot"}
[2025-06-23 10:22:30.019] [INFO] [General]: Initialized 5 test cases for discovery validation | {"service":"trading-bot"}
[2025-06-23 10:22:30.100] [INFO] [General]: Connected to MongoDB successfully | {"service":"trading-bot"}
[2025-06-23 10:22:30.103] [INFO] [General]: Server running on port 3002 | {"service":"trading-bot"}
[2025-06-23 10:22:30.104] [INFO] [General]: Environment: development | {"service":"trading-bot"}
[2025-06-23 10:22:30.134] [INFO] [General]: Data update job started | {"service":"trading-bot"}
[2025-06-23 10:22:36.336] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:22:36.344] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (8ms) | {"service":"trading-bot","tickerCount":5}
[2025-06-23 10:22:36.344] [INFO] [API]: GET /api/tickers - Status 200 (8ms) | {"service":"trading-bot","tickerCount":5}
[2025-06-23 10:22:36.362] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:22:36.370] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (8ms) | {"service":"trading-bot","tickerCount":5}
[2025-06-23 10:22:36.370] [INFO] [API]: GET /api/tickers - Status 200 (8ms) | {"service":"trading-bot","tickerCount":5}
[2025-06-23 10:22:47.404] [INFO] [API] - GET /api/tickers/XOM: Fetching specific ticker data | {"service":"trading-bot","ticker":"XOM","query":{}}
[2025-06-23 10:22:47.474] [INFO] [API] - GET /api/tickers/XOM: Successfully fetched ticker data (70ms) | {"service":"trading-bot","ticker":"XOM","lastUpdated":"2025-06-23T07:02:30.558Z","currentPrice":114.7,"newsCount":1}
[2025-06-23 10:22:47.475] [INFO] [API]: GET /api/tickers/XOM - Status 200 (70ms) | {"service":"trading-bot","ticker":"XOM","newsCount":1}
[2025-06-23 10:22:57.065] [INFO] [General]: Starting ticker discovery request | {"service":"trading-bot"}
[2025-06-23 10:22:57.065] [INFO] [Discovery] - Start: Starting ticker discovery process | {"service":"trading-bot","lookbackHours":24,"categories":["general","world","nation","technology","science","health"]}
[2025-06-23 10:22:58.484] [INFO] [General]: Global news fetched successfully | {"service":"trading-bot","totalArticles":66,"sources":["Politico","BBC News","The Washington Post","Understandingwar.org","Vox","Council on Foreign Relations","Phys.Org","Associated Press","SciTechDaily","Business Insider","NPR","TechCrunch","Eurogamer.net","Thinkstewartville.com","NDTV News","TooFab","The Daily Memphian","Entertainment Weekly","9to5google.com","RPG Site","MacRumors","The Daily Galaxy --Great Discoveries Channel","Space.com","Earth.com","Good News Network","Delish.com","Dailykos.com","PsyPost","Yahoo Entertainment","Heat.com","Albuquerque Journal","PBS","Ars Technica","Newsnationnow.com","Nintendo Life","Slate Magazine","Nintendoeverything.com","The Verge","Notebookcheck.net","Farmingdale-observer.com","ScienceAlert","IFLScience","Indiandefencereview.com","404media.co","St George News","Eatingwell.com","Vnexpress.net","Deccan Herald","Glassalmanac.com","Fox News"],"categories":["general","health","nation","science","technology"]}
[2025-06-23 10:22:58.487] [INFO] [Discovery] - SemanticFilter: Filtered 66 → 11 → 0 articles | {"service":"trading-bot","originalCount":66,"traditionalFiltered":11,"semanticallyFiltered":0}
[2025-06-23 10:22:58.488] [INFO] [Discovery] - Complete: Ticker discovery completed. Found 0 new tickers. | {"service":"trading-bot","totalNews":66,"relevantNews":0,"analyzedArticles":0,"discoveredTickers":0,"finalTickers":0}
[2025-06-23 10:22:58.489] [INFO] [General]: API | {"service":"trading-bot"}
[2025-06-23 10:23:56.326] [INFO] [General]: Starting ticker discovery request | {"service":"trading-bot"}
[2025-06-23 10:23:56.327] [INFO] [Discovery] - Start: Starting ticker discovery process | {"service":"trading-bot","lookbackHours":24,"categories":["general","world","nation","technology","science","health"]}
[2025-06-23 10:23:57.980] [INFO] [General]: Global news fetched successfully | {"service":"trading-bot","totalArticles":66,"sources":["Politico","BBC News","The Washington Post","Understandingwar.org","Vox","Council on Foreign Relations","Phys.Org","Associated Press","SciTechDaily","Business Insider","NPR","TechCrunch","Eurogamer.net","Thinkstewartville.com","NDTV News","TooFab","The Daily Memphian","Entertainment Weekly","9to5google.com","RPG Site","MacRumors","The Daily Galaxy --Great Discoveries Channel","Space.com","Earth.com","Good News Network","Delish.com","Dailykos.com","PsyPost","Yahoo Entertainment","Heat.com","Albuquerque Journal","PBS","Ars Technica","Newsnationnow.com","Nintendo Life","Slate Magazine","Nintendoeverything.com","The Verge","Notebookcheck.net","Farmingdale-observer.com","ScienceAlert","IFLScience","Indiandefencereview.com","404media.co","St George News","Eatingwell.com","Vnexpress.net","Deccan Herald","Glassalmanac.com","Fox News"],"categories":["general","health","nation","science","technology"]}
[2025-06-23 10:23:57.980] [INFO] [Discovery] - SemanticFilter: Filtered 66 → 11 → 0 articles | {"service":"trading-bot","originalCount":66,"traditionalFiltered":11,"semanticallyFiltered":0}
[2025-06-23 10:23:57.981] [INFO] [Discovery] - Complete: Ticker discovery completed. Found 0 new tickers. | {"service":"trading-bot","totalNews":66,"relevantNews":0,"analyzedArticles":0,"discoveredTickers":0,"finalTickers":0}
[2025-06-23 10:23:57.981] [INFO] [General]: API | {"service":"trading-bot"}
[2025-06-23 10:24:36.681] [INFO] [General]: [SYSTEM] SIGINT received. Shutting down... | {"service":"trading-bot"}
