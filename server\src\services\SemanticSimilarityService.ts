import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger } from '../utils/logger';
import { GlobalNewsArticle } from './GlobalNewsService';
import { CompanyProfile } from './CompanyProfileService';

export interface SemanticSimilarityResult {
  similarity: number; // 0-1
  confidence: number; // 0-100
  reasoning: string;
  keyMatches: string[];
}

export interface NewsEmbedding {
  title: string;
  content: string;
  embedding: number[];
  semanticKeywords: string[];
}

export interface CompanyEmbedding {
  ticker: string;
  businessModel: string;
  embedding: number[];
  semanticKeywords: string[];
}

export class SemanticSimilarityService {
  private static instance: SemanticSimilarityService;
  private embeddingCache: Map<string, number[]> = new Map();
  private readonly MODEL_NAME = 'gemini-embedding-exp-03-07';
  private readonly SIMILARITY_THRESHOLD = 0.3;
  private genAI: GoogleGenerativeAI | null = null;

  constructor(private googleAIApiKey?: string) {
    // Use Google AI API key if provided, otherwise will use local similarity methods
    if (googleAIApiKey) {
      this.genAI = new GoogleGenerativeAI(googleAIApiKey);
      logger.info('Google AI embeddings initialized');
    } else {
      logger.warn('No Google AI API key provided for embeddings, using fallback similarity methods');
    }
  }

  public static getInstance(googleAIApiKey?: string): SemanticSimilarityService {
    if (!SemanticSimilarityService.instance) {
      SemanticSimilarityService.instance = new SemanticSimilarityService(googleAIApiKey);
    }
    return SemanticSimilarityService.instance;
  }

  /**
   * Calculate semantic similarity between news article and company profile
   */
  public async calculateNewsSimilarity(
    article: GlobalNewsArticle,
    profile: CompanyProfile
  ): Promise<SemanticSimilarityResult> {
    try {
      // Create embeddings for news and company
      const newsEmbedding = await this.createNewsEmbedding(article);
      const companyEmbedding = await this.createCompanyEmbedding(profile);

      // Calculate cosine similarity
      const similarity = this.cosineSimilarity(newsEmbedding.embedding, companyEmbedding.embedding);

      // Find semantic keyword matches
      const keyMatches = this.findSemanticMatches(
        newsEmbedding.semanticKeywords,
        companyEmbedding.semanticKeywords
      );

      // Calculate confidence based on similarity and keyword matches
      const confidence = this.calculateConfidence(similarity, keyMatches.length);

      // Generate reasoning
      const reasoning = this.generateSimilarityReasoning(
        similarity,
        keyMatches,
        article.title,
        profile.businessModel
      );

      return {
        similarity,
        confidence,
        reasoning,
        keyMatches
      };
    } catch (error) {
      logger.error('Error calculating semantic similarity:', error);
      return this.getFallbackSimilarity(article, profile);
    }
  }

  /**
   * Create embedding for news article
   */
  private async createNewsEmbedding(article: GlobalNewsArticle): Promise<NewsEmbedding> {
    const text = `${article.title} ${article.content}`;
    const cacheKey = `news_${this.hashString(text)}`;

    let embedding = this.embeddingCache.get(cacheKey);
    if (!embedding) {
      embedding = await this.getEmbedding(text);
      this.embeddingCache.set(cacheKey, embedding);
    }

    return {
      title: article.title,
      content: article.content,
      embedding,
      semanticKeywords: this.extractSemanticKeywords(text, 'news')
    };
  }

  /**
   * Create embedding for company profile
   */
  private async createCompanyEmbedding(profile: CompanyProfile): Promise<CompanyEmbedding> {
    const text = `${profile.companyName} ${profile.businessModel} ${profile.primaryRevenue.join(' ')} ${profile.sector} ${profile.industry}`;
    const cacheKey = `company_${profile.ticker}`;

    let embedding = this.embeddingCache.get(cacheKey);
    if (!embedding) {
      embedding = await this.getEmbedding(text);
      this.embeddingCache.set(cacheKey, embedding);
    }

    return {
      ticker: profile.ticker,
      businessModel: profile.businessModel,
      embedding,
      semanticKeywords: this.extractSemanticKeywords(text, 'company')
    };
  }

  /**
   * Get embedding from Google AI API or fallback method
   */
  private async getEmbedding(text: string): Promise<number[]> {
    if (this.genAI) {
      try {
        const model = this.genAI.getGenerativeModel({ model: this.MODEL_NAME });
        const result = await model.embedContent(text.substring(0, 8000));

        if (result.embedding && result.embedding.values) {
          return result.embedding.values;
        } else {
          logger.warn('No embedding values returned from Google AI');
        }
      } catch (error) {
        logger.warn('Failed to get Google AI embedding, using fallback:', error);
      }
    }

    // Fallback to simple TF-IDF-like embedding
    return this.createFallbackEmbedding(text);
  }

  /**
   * Create fallback embedding using TF-IDF-like approach
   */
  private createFallbackEmbedding(text: string): number[] {
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    const wordFreq: { [key: string]: number } = {};
    
    // Calculate word frequencies
    words.forEach(word => {
      if (word.length > 2) { // Ignore very short words
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });

    // Create a fixed-size embedding vector (384 dimensions to match sentence transformers)
    const embedding = new Array(384).fill(0);
    const wordList = Object.keys(wordFreq);
    
    wordList.forEach((word, index) => {
      if (index < 384) {
        // Simple hash-based positioning with TF weighting
        const position = this.simpleHash(word) % 384;
        embedding[position] += wordFreq[word] / words.length;
      }
    });

    // Normalize the vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return magnitude > 0 ? embedding.map(val => val / magnitude) : embedding;
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private cosineSimilarity(vec1: number[], vec2: number[]): number {
    if (vec1.length !== vec2.length) {
      logger.warn('Vector length mismatch in cosine similarity calculation');
      return 0;
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i];
      norm1 += vec1[i] * vec1[i];
      norm2 += vec2[i] * vec2[i];
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
    return magnitude > 0 ? dotProduct / magnitude : 0;
  }

  /**
   * Extract semantic keywords from text
   */
  private extractSemanticKeywords(text: string, type: 'news' | 'company'): string[] {
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should']);
    
    // Filter out stop words and short words
    const meaningfulWords = words.filter(word => 
      word.length > 3 && 
      !stopWords.has(word) && 
      !/^\d+$/.test(word)
    );

    // Get word frequencies
    const wordFreq: { [key: string]: number } = {};
    meaningfulWords.forEach(word => {
      wordFreq[word] = (wordFreq[word] || 0) + 1;
    });

    // Sort by frequency and return top keywords
    const sortedWords = Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .map(([word]) => word);

    // Return top 10 keywords
    return sortedWords.slice(0, 10);
  }

  /**
   * Find semantic matches between keyword sets
   */
  private findSemanticMatches(newsKeywords: string[], companyKeywords: string[]): string[] {
    const matches: string[] = [];
    
    // Direct matches
    for (const newsWord of newsKeywords) {
      if (companyKeywords.includes(newsWord)) {
        matches.push(newsWord);
      }
    }

    // Semantic similarity matches (simple approach)
    for (const newsWord of newsKeywords) {
      for (const companyWord of companyKeywords) {
        if (this.areWordsSemanticallyRelated(newsWord, companyWord)) {
          matches.push(`${newsWord}~${companyWord}`);
        }
      }
    }

    return [...new Set(matches)]; // Remove duplicates
  }

  /**
   * Check if two words are semantically related
   */
  private areWordsSemanticallyRelated(word1: string, word2: string): boolean {
    // Simple semantic relationships
    const semanticGroups = [
      ['finance', 'financial', 'money', 'loan', 'credit', 'debt', 'banking'],
      ['technology', 'tech', 'software', 'digital', 'platform', 'system'],
      ['medical', 'health', 'healthcare', 'pharmaceutical', 'drug', 'treatment'],
      ['energy', 'power', 'electricity', 'oil', 'gas', 'renewable'],
      ['education', 'student', 'school', 'university', 'learning', 'academic'],
      ['government', 'federal', 'regulation', 'policy', 'regulatory', 'compliance']
    ];

    for (const group of semanticGroups) {
      if (group.includes(word1) && group.includes(word2)) {
        return true;
      }
    }

    // Check for word similarity (edit distance)
    return this.calculateEditDistance(word1, word2) <= 2 && Math.abs(word1.length - word2.length) <= 2;
  }

  /**
   * Calculate confidence based on similarity and matches
   */
  private calculateConfidence(similarity: number, matchCount: number): number {
    const similarityScore = similarity * 60; // Max 60 points from similarity
    const matchScore = Math.min(matchCount * 10, 40); // Max 40 points from matches
    return Math.min(100, similarityScore + matchScore);
  }

  /**
   * Generate reasoning for similarity score
   */
  private generateSimilarityReasoning(
    similarity: number,
    keyMatches: string[],
    newsTitle: string,
    businessModel: string
  ): string {
    const reasons: string[] = [];

    if (similarity > 0.8) {
      reasons.push('High semantic similarity between news content and business model');
    } else if (similarity > 0.6) {
      reasons.push('Moderate semantic similarity detected');
    } else if (similarity > 0.4) {
      reasons.push('Low semantic similarity found');
    } else {
      reasons.push('Minimal semantic relationship detected');
    }

    if (keyMatches.length > 0) {
      reasons.push(`Found ${keyMatches.length} semantic keyword matches: ${keyMatches.slice(0, 3).join(', ')}`);
    }

    if (similarity < this.SIMILARITY_THRESHOLD) {
      reasons.push('Below similarity threshold - potential false positive');
    }

    return reasons.join('; ');
  }

  /**
   * Get fallback similarity when embedding fails
   */
  private getFallbackSimilarity(
    article: GlobalNewsArticle,
    profile: CompanyProfile
  ): SemanticSimilarityResult {
    const newsText = `${article.title} ${article.content}`.toLowerCase();
    const companyText = `${profile.businessModel} ${profile.primaryRevenue.join(' ')}`.toLowerCase();
    
    // Simple word overlap calculation
    const newsWords = new Set(newsText.match(/\b\w+\b/g) || []);
    const companyWords = new Set(companyText.match(/\b\w+\b/g) || []);
    
    const intersection = new Set([...newsWords].filter(word => companyWords.has(word)));
    const union = new Set([...newsWords, ...companyWords]);
    
    const similarity = intersection.size / union.size;
    const keyMatches = Array.from(intersection).slice(0, 5);
    
    return {
      similarity,
      confidence: similarity * 100,
      reasoning: `Fallback word overlap similarity: ${intersection.size} common words out of ${union.size} total`,
      keyMatches
    };
  }

  /**
   * Simple hash function for strings
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Hash string for caching
   */
  private hashString(str: string): string {
    return this.simpleHash(str).toString(36);
  }

  /**
   * Calculate edit distance between two strings
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Check if similarity meets threshold
   */
  public meetsThreshold(similarity: number): boolean {
    return similarity >= this.SIMILARITY_THRESHOLD;
  }

  /**
   * Batch calculate similarities for multiple companies
   */
  public async batchCalculateSimilarity(
    article: GlobalNewsArticle,
    profiles: CompanyProfile[]
  ): Promise<Map<string, SemanticSimilarityResult>> {
    const results = new Map<string, SemanticSimilarityResult>();
    
    // Process in batches to avoid rate limits
    const batchSize = 5;
    for (let i = 0; i < profiles.length; i += batchSize) {
      const batch = profiles.slice(i, i + batchSize);
      const batchPromises = batch.map(async (profile) => {
        const result = await this.calculateNewsSimilarity(article, profile);
        return { ticker: profile.ticker, result };
      });

      const batchResults = await Promise.all(batchPromises);
      batchResults.forEach(({ ticker, result }) => {
        results.set(ticker, result);
      });

      // Add delay between batches
      if (i + batchSize < profiles.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return results;
  }
}
